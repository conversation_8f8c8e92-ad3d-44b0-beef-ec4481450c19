package main

import (
	"bufio"
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/gorilla/websocket"
)

// 定义常量
const (
	websocketURI   = "wss://matrix.tencent.com/ai_gen_txt_server/getClassify"
	captchaAPI     = "https://t.captcha.qq.com/cap_union_new_verify"
	browserCookies = `sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22196f394978c53c-09af953edbd4748-26011f51-1327104-196f394978d10ae%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk2ZjM5NDk3OGM1M2MtMDlhZjk1M2VkYmQ0NzQ4LTI2MDExZjUxLTEzMjcxMDQtMTk2ZjM5NDk3OGQxMGFlIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%22196f394978c53c-09af953edbd4748-26011f51-1327104-196f394978d10ae%22%7D; _gcl_au=1.1.750546091.1747843324; _ga_RPMZTEBERQ=GS2.1.s1747889012$o1$g1$t1747889512$j0$l0$h0; _ga=GA1.2.567199319.1747843324; qcloud_from=qcloud.bing.seo-1752807773858; qcstats_seo_keywords=%E9%80%9A%E7%94%A8%E6%8A%80%E6%9C%AF-%E5%85%B6%E4%BB%96-%E5%BC%80%E6%BA%90%2C%E5%85%B6%E4%BB%96-%E7%A9%BA%E7%B1%BB-%E5%B7%A5%E5%85%B7%2C%E5%85%B6%E4%BB%96-%E7%A9%BA%E7%B1%BB-%E7%9B%91%E6%8E%A7%2C%E5%85%B6`
)

// CaptchaResponse 定义验证码API响应结构
type CaptchaResponse struct {
	Ticket  string `json:"ticket"`
	Randstr string `json:"randstr"`
}

// 获取验证码凭证
func getCaptchaCredentials() (string, string, error) {
	// 构建请求数据
	data := url.Values{}
	data.Set("collect", "UdBBi3tHrEn9VZ%2FmwdhLkz%2FB1%2Bg85F6VP2Rb0wTcJgAiq1pQgaD40mgIeBdGDs76KYA6VDSTqIGkEfWtzxjR7YM7nvybB%2FPqQVRh1CaknrN3er5EWqzyMIo9fGhSzALq4F2iavhs4usNukVk0HHOquwgMjBVK3y5CNBqDwdaLGZl%2FgqclmG5ajo%2FYJZFoIbYNhdXjVVfIY%2Bn4LMs6icq3KOIgrLQq0UfZMABe4lUSOkhR6rikgwPI3HAwf4RcB%2B1XXwc8aFBQ5ZA0HNgBq8g2GyNBcmN%2FVWrH8I5SOlYy40l2lDWnKfGlTBJGOv5tNI4UENBASM8U4FEaAeNKlBnXATQUEAgkSi67XT8ypXAlaEdd2LlhNnVe8Q6Zf2G90ndDbpFZNBxzqrdva4Hzx3aZAOhI9UwpFfwXEsEEpB4YVjmFvYTbIBYFYhJxLQdInoge0GyM8%2FP3JASx1ut7gDA6c5DIFa1%2Bxqm3wz5%2F73%2FdPxISaqTq%2BJOLz10997ZPiac1dJ2WYCPnNfEh2h%2BRPxEEJbI7qcSJvk3rri%2BIQ%2BG8DKdQHoWJDuXK2ZBl6Uv0gXvM2O1Up%2FBW9Tzpvd%2BZsIWJnwvFOs6CpMEr48eMijWpWwm%2Bx9euNAOyvYHF%2FGuDgZNvuhgMkgz4towGTHQxiRJP5WzFhZQYvaqadZBsz%2Bdx%2B0gpIws%2Fjud7LwavWvA%2F9P8kNq15mDSwNUbtjC%2BAfzCPjJnP6xjHrfdlQTGQCC5rMKcDj3LIkUQ4SHk9HMzl8H4r%2FZy9xpT8JkU5F9wNdDR7cFylPV%2B1svnuq0G%2BV17IjJsIrtVNX%2Fcnm1vqzigdbY5p37uMoIT8BPDiSP%2F7zo0qkk9Pvb99pEyZGtaLqi6%2FgAhDtF461aSIl18HPGhQUOWEHqPtuHH4bzm3p%2F%2BewEaruben%2F57ARquPCA8qP7RrsZJPT72%2FfaRMuben%2F57ARqu1mQVaar09nK%2FyWUhMj645O4EFnpn%2BGMGOm2EcoZT8M5HTO1DR%2Bm8hIsBA%2Fy%2F5tk1")
	data.Set("tlg", "1024")
	data.Set("eks", "nB%2FVw2ehev5p1mQ12h7%2BDWM9azxqv6ckk8JSN3rUs6fTVbzCoD9AIHjDQxJa3jHJZNLaMq%2BM3bboRusu%2Baft9oJeJLj7ut5iG1tInc6HXvbpjmBxGxn%2FDW4mZOWFlycezFvpKEuchOOvbPFRqNq3J%2Bd%2FfLat7MAyZ6dasnDaaWALFdIxtOvahK64%2B3gNtnuvbySXjWByxFSVWWDM%2Bdq5WISIxRPVhkAz73YV9uqlJas%3D")
	data.Set("sess", "s0WCgHckJ5KUQLBtay5Y_AluS1FtXIVPbmrJI8gK9Qk1WW-nEUzYp6d1sVklKMggVXnldyAJYVjFMcuvQekn0vsXsbRRKoahZnVe43oCrpgt19TXgqfUxluGhHVBNHm8rPwBeNK-wa-ZUYWMf-RuzrL3RHRVYNJyBBBwwZQW0rg7dJsdRq_diGFJhrUTCWFQBmkYSvV4lpMVJLazbyP4pi2E7VOaHoFe16yNWEfrGLB34L7kJl6LjVnYYkmV849_HVcEI4uJzrk2uNwKCo9QMexnWGXHhRzI5XaujHioDMtHBFLc7TJFNL4YLYwGYOxa4EdmPgHYAa82qnZW3vMH7_TYRG7m61XMV3hxYpQI_7kUgpDcYNkVI6GM2Is0TilcyICCAgwGJs1Om_ZY8a4jMuZqizT2uXHGEpc_TWhK4BBGtQFVdxqmUW007l3lPOugwf")
	data.Set("ans", "%5B%7B%22elem_id%22%3A0%2C%22type%22%3A%22DynAnswerType_TIME%22%2C%22data%22%3A%22%22%7D%5D")
	data.Set("pow_answer", "dc3ec1e1475de216%23808")
	data.Set("pow_calc_time", "21")

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 创建请求
	req, err := http.NewRequest("POST", captchaAPI, bytes.NewBufferString(data.Encode()))
	if err != nil {
		return "", "", fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Accept", "application/json, text/javascript, */*; q=0.01")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
	req.Header.Set("Origin", "https://captcha.gtimg.com")
	req.Header.Set("Referer", "https://captcha.gtimg.com/")
	req.Header.Set("Sec-Fetch-Dest", "empty")
	req.Header.Set("Sec-Fetch-Mode", "cors")
	req.Header.Set("Sec-Fetch-Site", "cross-site")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	req.Header.Set("sec-ch-ua", `"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"`)
	req.Header.Set("sec-ch-ua-mobile", "?0")
	req.Header.Set("sec-ch-ua-platform", `"Windows"`)

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return "", "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", "", fmt.Errorf("读取响应失败: %v", err)
	}

	fmt.Printf("🔑 验证码API响应: %s\n", string(body))

	// 解析响应 - 根据实际API响应格式调整
	var captchaResp map[string]interface{}
	if err := json.Unmarshal(body, &captchaResp); err != nil {
		return "", "", fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查API响应
	if errorCode, ok := captchaResp["errorCode"].(string); ok && errorCode != "0" {
		// API返回错误，使用默认凭证
		fmt.Printf("⚠️ API返回错误码: %s，使用默认凭证\n", errorCode)
		return "t03I-LEsLWnsGBT3OKL4Q0bOjGfwOc-cB9vRbE07iWvRIPmzYZuuaXVOi_h-L6QFNaxcZnEBnp9sem3g6tMcx1ah_VUKxC1EhnalM9rMvUxuyiYxNjy27SoIcEQZmWPe0PNiqaS3mW3TVQ4uhtdpKectW8FkJqcOtwYTsYVZBD6kuPsjPRfzxB77Q**", "@HyN", nil
	}

	// 提取ticket和randstr
	ticket, _ := captchaResp["ticket"].(string)
	randstr, _ := captchaResp["randstr"].(string)

	// 如果获取到的凭证为空，使用默认凭证
	if ticket == "" || randstr == "" {
		fmt.Println("⚠️ 获取到的凭证为空，使用默认凭证")
		return "t03I-LEsLWnsGBT3OKL4Q0bOjGfwOc-cB9vRbE07iWvRIPmzYZuuaXVOi_h-L6QFNaxcZnEBnp9sem3g6tMcx1ah_VUKxC1EhnalM9rMvUxuyiYxNjy27SoIcEQZmWPe0PNiqaS3mW3TVQ4uhtdpKectW8FkJqcOtwYTsYVZBD6kuPsjPRfzxB77Q**", "@HyN", nil
	}

	return ticket, randstr, nil
}

var yourText = `在中国"嫦娥一号"探月卫星于明年发射之后,中国还将发射三颗"夸父"卫星"逐日"。昨天,在第36届世界空间科技大会期间，中国宇航局发言人正式对外宣布了这一宏伟计划。这一计划的实施，不仅标志着中国航天事业在深空探测领域取得了新的重大突破，也展示了中国政府对于未来航天发展的宏伟蓝图和坚定信念。
"夸父"卫星项目，作为中国古代神话中追逐太阳的英雄的名字，寓意着探索太阳的无限可能。这一项目计划在未来几年内，通过发射三颗卫星，实现对太阳的全面、深入观测。这些卫星将携带先进的观测仪器，对太阳进行全方位、多角度的观测，获取大量关于太阳的宝贵数据。这些数据不仅有助于科学家们更深入地理解太阳的物理特性，为未来的空间天气预报提供更为精准的数据支持，还将推动相关领域的科学研究和技术创新。
第一颗"夸父"卫星，计划在未来两年内发射。这颗卫星的主要任务是进行太阳风的综合观测。通过搭载的高精度仪器，它将实时监测太阳风的速度、方向、温度等关键参数，为科学家们提供太阳风的详尽数据。这些数据将有助于科学家们更好地理解太阳风对地球空间环境的影响，预测太阳活动对地球空间环境的潜在威胁，为航天器的安全运行提供更为可靠的保障。
第二颗"夸父"卫星，将在第一颗卫星发射后的两年内发射。这颗卫星的主要目标是观测太阳耀斑和日冕物质抛射现象。通过搭载的先进观测设备，它将捕捉到太阳耀斑和日冕物质抛射的清晰图像和数据，揭示这些现象背后的物理机制。这些数据将有助于科学家们更好地预测太阳活动，降低太阳活动对地球空间环境和人类活动的影响，为地球空间环境的稳定和安全提供有力支持。
第三颗"夸父"卫星，则计划在第二颗卫星发射后的两年内发射。这颗卫星的主要任务是进行太阳和地球空间环境的联合观测。通过搭载的多波段观测仪器，它将同时观测太阳和地球空间环境的变化，揭示它们之间的相互作用和影响机制。这些数据将有助于科学家们更好地理解太阳活动对地球空间环境的影响，为航天器的运行和人类活动提供更为准确的空间天气预报。
"夸父"卫星项目的实施，无疑将为中国的航天事业注入新的活力。通过这一项目，中国不仅将进一步提升在深空探测领域的技术水平，还将为全球空间科学的发展做出重要贡献。同时，"夸父"卫星项目也将激发更多年轻人对航天科技的兴趣和热情，为中国航天事业的持续发展培养更多的优秀人才。`

// 处理单次文本分析
func processText(conn *websocket.Conn, text string) error {
	fmt.Printf("\n🔄 开始处理文本分析...\n")

	// 1. 获取新的验证码凭证
	fmt.Println("🔑 正在获取验证码凭证...")
	ticket, randstr, err := getCaptchaCredentials()
	if err != nil {
		return fmt.Errorf("获取验证码凭证失败: %v", err)
	}
	// 安全地显示ticket信息
	ticketDisplay := ticket
	if len(ticket) > 20 {
		ticketDisplay = ticket[:20] + "..."
	}
	fmt.Printf("✅ 获取到凭证 - ticket: %s, randstr: %s\n", ticketDisplay, randstr)

	// 2. 发送指纹信息
	fpPayload, _ := json.Marshal(map[string]string{"fp": "a2b4126d8a97995ecb07205636889192"})
	err = conn.WriteMessage(websocket.TextMessage, fpPayload)
	if err != nil {
		return fmt.Errorf("发送指纹失败: %v", err)
	}
	fmt.Printf("  ⬆️ 已发送指纹: %s\n", fpPayload)

	// 接收状态响应
	_, message, err := conn.ReadMessage()
	if err != nil {
		return fmt.Errorf("读取状态失败: %v", err)
	}
	fmt.Printf("  ⬇️ 收到状态: %s\n", message)

	// 3. 发送人机验证凭证
	ticketPayload, _ := json.Marshal(map[string]string{
		"ticket":  ticket,
		"randstr": randstr,
	})
	err = conn.WriteMessage(websocket.TextMessage, ticketPayload)
	if err != nil {
		return fmt.Errorf("发送凭证失败: %v", err)
	}
	fmt.Printf("  ⬆️ 已发送凭证: %s\n", ticketPayload)

	// 接收验证结果
	_, message, err = conn.ReadMessage()
	if err != nil {
		return fmt.Errorf("读取验证结果失败: %v", err)
	}
	fmt.Printf("  ⬇️ 收到验证结果: %s\n", message)

	// 4. 发送文本内容
	textPayload, _ := json.Marshal(map[string]string{"text": text})
	err = conn.WriteMessage(websocket.TextMessage, textPayload)
	if err != nil {
		return fmt.Errorf("发送文本失败: %v", err)
	}
	fmt.Printf("  ⬆️ 已发送文本内容\n")

	fmt.Println("✅ 数据已全部发送，正在等待服务器处理结果...")

	// 循环接收服务器返回的消息
	for {
		_, message, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsCloseError(err, websocket.CloseNormalClosure, websocket.CloseGoingAway) {
				return fmt.Errorf("连接被关闭: %v", err)
			} else {
				return fmt.Errorf("读取消息失败: %v", err)
			}
		}

		fmt.Printf("  ⬇️ 收到消息: %s\n", message)

		// 解析消息判断是否为最终结果
		var result map[string]interface{}
		if json.Unmarshal(message, &result) == nil {
			if status, ok := result["status"].(string); ok && status == "success" {
				if _, hasLabels := result["labels_ratio"]; hasLabels {
					fmt.Println("🎉 分析完成！")
					return nil
				}
			}
		}
	}
}

func main() {
	// 配置 TLS
	tlsConfig := &tls.Config{
		RootCAs: nil,
	}

	// 设置 WebSocket 拨号器，添加完整的请求头
	dialer := websocket.Dialer{
		TLSClientConfig: tlsConfig,
	}

	// 设置完整的请求头
	headers := http.Header{
		"Origin":          []string{"https://matrix.tencent.com"},
		"Cache-Control":   []string{"no-cache"},
		"Accept-Language": []string{"zh-CN,zh;q=0.9"},
		"Pragma":          []string{"no-cache"},
		"User-Agent":      []string{"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},
		"Cookie":          []string{browserCookies},
	}

	fmt.Println("🚀 启动持续WebSocket连接...")

	for {
		// 建立 WebSocket 连接
		fmt.Println("\n🔗 正在建立WebSocket连接...")
		conn, _, err := dialer.Dial(websocketURI, headers)
		if err != nil {
			fmt.Printf("❌ 连接失败: %v，5秒后重试...\n", err)
			time.Sleep(5 * time.Second)
			continue
		}
		fmt.Println("✅ WebSocket 连接已成功建立")

		// 处理文本分析
		err = processText(conn, yourText)
		if err != nil {
			fmt.Printf("❌ 处理失败: %v\n", err)
		}

		conn.Close()
		fmt.Println("\n⏰ 等待10秒后进行下一次分析...")
		time.Sleep(10 * time.Second)

		// 询问用户是否继续
		fmt.Print("\n是否继续下一次分析？(y/n): ")
		reader := bufio.NewReader(os.Stdin)
		input, _ := reader.ReadString('\n')
		input = strings.TrimSpace(strings.ToLower(input))

		if input == "n" || input == "no" {
			fmt.Println("👋 程序结束")
			break
		}
	}
}
